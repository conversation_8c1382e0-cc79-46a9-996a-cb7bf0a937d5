"""
Context Analyzer - Analyzes learning context and user experience
Uses VinAgent's agent patterns for intelligent context assessment
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import logging

from vinagent.agent.agent import Agent
from langchain_together import ChatTogether

logger = logging.getLogger(__name__)

@dataclass
class UserProfile:
    """User learning profile structure"""
    learner_id: str
    experience_levels: Dict[str, str]
    learning_preferences: Dict[str, Any]
    framework_experience: Dict[str, str]
    learning_history: List[Dict[str, Any]]
    goals: List[str]

@dataclass
class ContextAnalysis:
    """Result of context analysis"""
    target_framework: str
    experience_level: str
    learning_phase: str
    recommended_approach: str
    complexity_level: str
    estimated_duration: str
    prerequisites: List[str]
    learning_path: List[str]

class ContextAnalyzer(Agent):
    """
    Analyzes learning context to determine optimal learning approach
    Extends VinAgent's Agent class for intelligent context assessment
    """
    
    def __init__(self, llm: Optional[ChatTogether] = None):
        if llm is None:
            llm = ChatTogether(model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free")
        
        super().__init__(
            llm=llm,
            description="Learning Context Analyzer for Educational Framework Learning",
            skills=[
                "Analyze user learning profiles and experience levels",
                "Assess framework-specific knowledge and skills",
                "Determine optimal learning approaches and paths",
                "Identify knowledge gaps and prerequisites",
                "Recommend personalized learning strategies"
            ],
            tools=[
                'tools.educational_tools.profile_analyzer',
                'tools.educational_tools.skill_assessor',
                'tools.educational_tools.learning_path_generator'
            ],
            memory_path='data/sessions/context_analyzer_memory.json'
        )
        
        # Framework knowledge base
        self.framework_knowledge = self._load_framework_knowledge()
        
        logger.info("ContextAnalyzer initialized")
    
    def analyze_learning_context(self, user_profile: UserProfile, 
                                learning_objective: str) -> ContextAnalysis:
        """
        Perform comprehensive learning context analysis
        
        Args:
            user_profile: User's learning profile and experience
            learning_objective: Specific learning objective
            
        Returns:
            ContextAnalysis: Comprehensive context analysis result
        """
        logger.info(f"Analyzing learning context for objective: {learning_objective}")
        
        # Extract target framework
        target_framework = self._identify_target_framework(learning_objective)
        
        # Assess user experience level
        experience_level = self._assess_framework_experience(user_profile, target_framework)
        
        # Determine learning phase
        learning_phase = self._determine_learning_phase(
            experience_level, learning_objective, user_profile
        )
        
        # Recommend learning approach
        recommended_approach = self._recommend_learning_approach(
            user_profile, target_framework, experience_level
        )
        
        # Determine complexity level
        complexity_level = self._determine_complexity_level(
            experience_level, learning_objective
        )
        
        # Estimate learning duration
        estimated_duration = self._estimate_learning_duration(
            target_framework, experience_level, learning_phase
        )
        
        # Identify prerequisites
        prerequisites = self._identify_prerequisites(
            target_framework, experience_level, user_profile
        )
        
        # Generate learning path
        learning_path = self._generate_learning_path(
            target_framework, experience_level, learning_objective
        )
        
        analysis = ContextAnalysis(
            target_framework=target_framework,
            experience_level=experience_level,
            learning_phase=learning_phase,
            recommended_approach=recommended_approach,
            complexity_level=complexity_level,
            estimated_duration=estimated_duration,
            prerequisites=prerequisites,
            learning_path=learning_path
        )
        
        logger.info(f"Context analysis completed for {target_framework} at {experience_level} level")
        return analysis
    
    def _identify_target_framework(self, learning_objective: str) -> str:
        """Identify the target framework from learning objective"""
        objective_lower = learning_objective.lower()
        
        # Framework detection patterns
        framework_patterns = {
            'langchain': ['langchain', 'lang chain', 'lc'],
            'crewai': ['crewai', 'crew ai', 'crew-ai'],
            'multi_agent': ['multi-agent', 'multi agent', 'agent orchestration'],
            'langgraph': ['langgraph', 'lang graph'],
            'autogen': ['autogen', 'auto gen'],
            'llamaindex': ['llamaindex', 'llama index', 'llama-index']
        }
        
        for framework, patterns in framework_patterns.items():
            if any(pattern in objective_lower for pattern in patterns):
                return framework
        
        # Default to langchain if no specific framework detected
        return 'langchain'
    
    def _assess_framework_experience(self, user_profile: UserProfile, 
                                   framework: str) -> str:
        """Assess user's experience level with specific framework"""
        
        # Check direct framework experience
        if framework in user_profile.framework_experience:
            return user_profile.framework_experience[framework]
        
        # Check related framework experience
        related_frameworks = self._get_related_frameworks(framework)
        related_experience = []
        
        for related_fw in related_frameworks:
            if related_fw in user_profile.framework_experience:
                related_experience.append(user_profile.framework_experience[related_fw])
        
        if related_experience:
            # Use highest related experience level
            experience_levels = ['beginner', 'intermediate', 'advanced']
            max_level = max(related_experience, key=lambda x: experience_levels.index(x))
            
            # Reduce by one level for related experience
            if max_level == 'advanced':
                return 'intermediate'
            elif max_level == 'intermediate':
                return 'beginner'
            else:
                return 'beginner'
        
        # Fallback to general AI/programming experience
        ai_experience = user_profile.experience_levels.get('ai_frameworks', 'beginner')
        programming_experience = user_profile.experience_levels.get('programming', 'beginner')
        
        # Combine AI and programming experience
        if ai_experience == 'advanced' and programming_experience == 'advanced':
            return 'intermediate'  # Can start at intermediate with strong background
        elif ai_experience in ['intermediate', 'advanced'] or programming_experience in ['intermediate', 'advanced']:
            return 'beginner'  # Start at beginner but can progress faster
        else:
            return 'beginner'
    
    def _determine_learning_phase(self, experience_level: str, 
                                learning_objective: str, 
                                user_profile: UserProfile) -> str:
        """Determine appropriate learning phase"""
        
        objective_lower = learning_objective.lower()
        
        # Phase detection based on keywords
        if any(keyword in objective_lower for keyword in ['introduction', 'basics', 'getting started']):
            return 'introduction'
        elif any(keyword in objective_lower for keyword in ['fundamentals', 'core concepts']):
            return 'fundamentals'
        elif any(keyword in objective_lower for keyword in ['hands-on', 'practice', 'build', 'create']):
            return 'hands_on_practice'
        elif any(keyword in objective_lower for keyword in ['advanced', 'complex', 'optimization']):
            return 'advanced_concepts'
        elif any(keyword in objective_lower for keyword in ['project', 'application', 'real-world']):
            return 'project_application'
        
        # Phase based on experience level
        if experience_level == 'beginner':
            return 'introduction'
        elif experience_level == 'intermediate':
            return 'hands_on_practice'
        else:
            return 'advanced_concepts'
    
    def _recommend_learning_approach(self, user_profile: UserProfile, 
                                   framework: str, experience_level: str) -> str:
        """Recommend optimal learning approach"""
        
        learning_style = user_profile.learning_preferences.get('style', 'balanced')
        
        if learning_style == 'hands_on':
            return 'practice_first_with_theory'
        elif learning_style == 'theoretical':
            return 'theory_first_with_practice'
        elif experience_level == 'beginner':
            return 'guided_step_by_step'
        elif experience_level == 'advanced':
            return 'exploration_with_challenges'
        else:
            return 'balanced_theory_practice'
    
    def _determine_complexity_level(self, experience_level: str, 
                                  learning_objective: str) -> str:
        """Determine appropriate complexity level"""
        
        objective_lower = learning_objective.lower()
        
        # Complexity indicators in objective
        if any(keyword in objective_lower for keyword in ['simple', 'basic', 'easy']):
            return 'basic'
        elif any(keyword in objective_lower for keyword in ['complex', 'advanced', 'sophisticated']):
            return 'advanced'
        elif any(keyword in objective_lower for keyword in ['intermediate', 'moderate']):
            return 'intermediate'
        
        # Complexity based on experience
        complexity_mapping = {
            'beginner': 'basic',
            'intermediate': 'intermediate',
            'advanced': 'advanced'
        }
        
        return complexity_mapping.get(experience_level, 'basic')
    
    def _estimate_learning_duration(self, framework: str, experience_level: str, 
                                  learning_phase: str) -> str:
        """Estimate learning duration based on framework and experience"""
        
        # Base duration estimates (in hours)
        base_durations = {
            'langchain': {
                'beginner': {'introduction': 8, 'fundamentals': 16, 'hands_on_practice': 24},
                'intermediate': {'fundamentals': 8, 'hands_on_practice': 16, 'advanced_concepts': 24},
                'advanced': {'hands_on_practice': 8, 'advanced_concepts': 16, 'project_application': 32}
            },
            'crewai': {
                'beginner': {'introduction': 6, 'fundamentals': 12, 'hands_on_practice': 20},
                'intermediate': {'fundamentals': 6, 'hands_on_practice': 12, 'advanced_concepts': 20},
                'advanced': {'hands_on_practice': 6, 'advanced_concepts': 12, 'project_application': 24}
            }
        }
        
        # Get duration or use default
        duration_hours = base_durations.get(framework, base_durations['langchain']).get(
            experience_level, {}
        ).get(learning_phase, 16)
        
        # Convert to human-readable format
        if duration_hours <= 8:
            return f"{duration_hours} hours (1-2 sessions)"
        elif duration_hours <= 16:
            return f"{duration_hours} hours (2-3 sessions)"
        elif duration_hours <= 24:
            return f"{duration_hours} hours (3-4 sessions)"
        else:
            return f"{duration_hours} hours (4+ sessions)"
    
    def _identify_prerequisites(self, framework: str, experience_level: str, 
                              user_profile: UserProfile) -> List[str]:
        """Identify learning prerequisites"""
        
        prerequisites = []
        
        # Framework-specific prerequisites
        framework_prereqs = {
            'langchain': {
                'beginner': ['Basic Python programming', 'Understanding of APIs', 'Basic AI/ML concepts'],
                'intermediate': ['Python programming', 'REST APIs', 'LLM fundamentals'],
                'advanced': ['Advanced Python', 'AI/ML experience', 'System design basics']
            },
            'crewai': {
                'beginner': ['Basic Python programming', 'Understanding of AI agents', 'LangChain basics'],
                'intermediate': ['Python programming', 'LangChain experience', 'Multi-agent concepts'],
                'advanced': ['Advanced Python', 'LangChain proficiency', 'System orchestration']
            }
        }
        
        # Get prerequisites for framework and level
        prereqs = framework_prereqs.get(framework, framework_prereqs['langchain']).get(
            experience_level, []
        )
        
        # Filter based on user's existing skills
        user_skills = user_profile.experience_levels
        
        for prereq in prereqs:
            if 'python' in prereq.lower():
                if user_skills.get('programming', 'beginner') == 'beginner':
                    prerequisites.append(prereq)
            elif 'api' in prereq.lower():
                if user_skills.get('web_development', 'beginner') == 'beginner':
                    prerequisites.append(prereq)
            elif 'ai' in prereq.lower() or 'ml' in prereq.lower():
                if user_skills.get('ai_frameworks', 'beginner') == 'beginner':
                    prerequisites.append(prereq)
            else:
                prerequisites.append(prereq)
        
        return prerequisites
    
    def _generate_learning_path(self, framework: str, experience_level: str, 
                              learning_objective: str) -> List[str]:
        """Generate personalized learning path"""
        
        # Framework-specific learning paths
        learning_paths = {
            'langchain': {
                'beginner': [
                    'Introduction to LangChain and LLMs',
                    'Setting up LangChain environment',
                    'Basic chains and prompts',
                    'Working with different LLM providers',
                    'Document loading and processing',
                    'Building simple applications',
                    'Memory and conversation handling'
                ],
                'intermediate': [
                    'Advanced chain composition',
                    'Custom tools and agents',
                    'Vector databases and retrieval',
                    'LangChain Expression Language (LCEL)',
                    'Streaming and async operations',
                    'Production deployment considerations'
                ],
                'advanced': [
                    'Custom LangChain components',
                    'Advanced agent architectures',
                    'Performance optimization',
                    'Multi-modal applications',
                    'Enterprise integration patterns',
                    'Contributing to LangChain ecosystem'
                ]
            },
            'crewai': {
                'beginner': [
                    'Introduction to CrewAI and multi-agent systems',
                    'Setting up CrewAI environment',
                    'Creating your first crew',
                    'Defining agents and their roles',
                    'Task assignment and execution',
                    'Basic crew coordination patterns'
                ],
                'intermediate': [
                    'Advanced crew configurations',
                    'Custom tools for agents',
                    'Inter-agent communication',
                    'Crew performance optimization',
                    'Error handling and recovery',
                    'Integration with external systems'
                ],
                'advanced': [
                    'Complex multi-crew orchestration',
                    'Custom agent architectures',
                    'Advanced coordination patterns',
                    'Scalability and performance tuning',
                    'Enterprise deployment strategies',
                    'Contributing to CrewAI ecosystem'
                ]
            }
        }
        
        return learning_paths.get(framework, learning_paths['langchain']).get(
            experience_level, learning_paths['langchain']['beginner']
        )
    
    def _get_related_frameworks(self, framework: str) -> List[str]:
        """Get frameworks related to the target framework"""
        
        related_frameworks = {
            'langchain': ['llamaindex', 'autogen', 'langgraph'],
            'crewai': ['langchain', 'autogen', 'multi_agent'],
            'langgraph': ['langchain', 'crewai'],
            'autogen': ['langchain', 'crewai'],
            'llamaindex': ['langchain', 'langgraph']
        }
        
        return related_frameworks.get(framework, [])
    
    def _load_framework_knowledge(self) -> Dict[str, Any]:
        """Load framework knowledge base"""
        return {
            'langchain': {
                'description': 'Framework for developing applications powered by language models',
                'key_concepts': ['chains', 'prompts', 'agents', 'memory', 'tools'],
                'difficulty': 'intermediate',
                'prerequisites': ['python', 'apis', 'llm_basics']
            },
            'crewai': {
                'description': 'Framework for orchestrating role-playing autonomous AI agents',
                'key_concepts': ['crews', 'agents', 'tasks', 'roles', 'coordination'],
                'difficulty': 'intermediate',
                'prerequisites': ['python', 'langchain', 'multi_agent_concepts']
            }
        }
