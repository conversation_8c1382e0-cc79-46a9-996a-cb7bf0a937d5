"""
Constellation Manager - Dynamic formation of learning agent constellations
Uses VinAgent's tool registration system for educational agent coordination
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

from vinagent.agent.agent import Agent
from langchain_together import ChatTogether

logger = logging.getLogger(__name__)

class ConstellationType(Enum):
    """Types of learning constellations"""
    KNOWLEDGE_ACQUISITION = "knowledge_acquisition"
    HANDS_ON_PRACTICE = "hands_on_practice"
    ASSESSMENT_EVALUATION = "assessment_evaluation"
    MIXED_LEARNING = "mixed_learning"

@dataclass
class ConstellationConfig:
    """Configuration for constellation formation"""
    constellation_type: ConstellationType
    framework: str
    experience_level: str
    learning_style: str
    agent_roles: List[str]
    coordination_pattern: str

class LearningConstellation:
    """
    A constellation of learning agents working together
    Based on VinAgent's agent coordination patterns
    """
    
    def __init__(self, constellation_id: str, config: ConstellationConfig, agents: List[Agent]):
        self.constellation_id = constellation_id
        self.config = config
        self.agents = agents
        self.coordinator_agent = None
        self.active = False
        
        # Set up coordination
        self._setup_coordination()
    
    def _setup_coordination(self):
        """Set up agent coordination within constellation"""
        if self.agents:
            # First agent becomes coordinator
            self.coordinator_agent = self.agents[0]
            logger.info(f"Constellation {self.constellation_id} coordinator set: {self.coordinator_agent.description}")
    
    def activate(self):
        """Activate the constellation for learning"""
        self.active = True
        logger.info(f"Constellation {self.constellation_id} activated")
    
    def deactivate(self):
        """Deactivate the constellation"""
        self.active = False
        logger.info(f"Constellation {self.constellation_id} deactivated")
    
    def get_primary_agent(self) -> Optional[Agent]:
        """Get the primary agent for this constellation"""
        return self.coordinator_agent
    
    def get_agents_by_role(self, role: str) -> List[Agent]:
        """Get agents by their role in the constellation"""
        return [agent for agent in self.agents if role in agent.description.lower()]

class ConstellationManager:
    """
    Manages dynamic formation and coordination of learning constellations
    Uses VinAgent's architecture for agent management
    """
    
    def __init__(self):
        self.active_constellations: Dict[str, LearningConstellation] = {}
        self.constellation_templates = self._load_constellation_templates()
        
        logger.info("ConstellationManager initialized")
    
    def form_constellation(self, framework: str, experience_level: str, 
                         learning_phase: str, learning_style: str) -> LearningConstellation:
        """
        Form optimal learning constellation based on learning context
        
        Args:
            framework: Target framework (langchain, crewai, etc.)
            experience_level: User's experience level
            learning_phase: Current learning phase
            learning_style: User's learning style preference
            
        Returns:
            LearningConstellation: Formed constellation ready for learning
        """
        logger.info(f"Forming constellation for {framework} - {experience_level} - {learning_phase}")
        
        # Determine constellation type
        constellation_type = self._determine_constellation_type(learning_phase, learning_style)
        
        # Get constellation configuration
        config = self._get_constellation_config(
            constellation_type, framework, experience_level, learning_style
        )
        
        # Create agents for constellation
        agents = self._create_constellation_agents(config)
        
        # Create constellation
        constellation_id = f"constellation_{framework}_{experience_level}_{learning_phase}"
        constellation = LearningConstellation(constellation_id, config, agents)
        
        # Store active constellation
        self.active_constellations[constellation_id] = constellation
        
        # Activate constellation
        constellation.activate()
        
        logger.info(f"Constellation {constellation_id} formed with {len(agents)} agents")
        return constellation
    
    def _determine_constellation_type(self, learning_phase: str, learning_style: str) -> ConstellationType:
        """Determine appropriate constellation type"""
        if learning_phase in ['introduction', 'basic_concepts']:
            return ConstellationType.KNOWLEDGE_ACQUISITION
        elif learning_phase in ['hands_on_practice', 'practice_focused']:
            return ConstellationType.HANDS_ON_PRACTICE
        elif learning_phase in ['assessment', 'evaluation']:
            return ConstellationType.ASSESSMENT_EVALUATION
        else:
            return ConstellationType.MIXED_LEARNING
    
    def _get_constellation_config(self, constellation_type: ConstellationType, 
                                framework: str, experience_level: str, 
                                learning_style: str) -> ConstellationConfig:
        """Get configuration for constellation formation"""
        
        # Base agent roles for different constellation types
        if constellation_type == ConstellationType.KNOWLEDGE_ACQUISITION:
            agent_roles = [
                "research_specialist",
                "concept_explainer", 
                "documentation_parser",
                "comparison_analyst"
            ]
            coordination_pattern = "sequential_with_feedback"
            
        elif constellation_type == ConstellationType.HANDS_ON_PRACTICE:
            agent_roles = [
                "tutorial_guide",
                "exercise_generator",
                "code_demonstrator",
                "practice_mentor"
            ]
            coordination_pattern = "interactive_guidance"
            
        elif constellation_type == ConstellationType.ASSESSMENT_EVALUATION:
            agent_roles = [
                "quiz_generator",
                "progress_evaluator",
                "feedback_provider",
                "competency_assessor"
            ]
            coordination_pattern = "evaluation_pipeline"
            
        else:  # MIXED_LEARNING
            agent_roles = [
                "learning_coordinator",
                "adaptive_tutor",
                "progress_tracker",
                "resource_provider"
            ]
            coordination_pattern = "adaptive_coordination"
        
        return ConstellationConfig(
            constellation_type=constellation_type,
            framework=framework,
            experience_level=experience_level,
            learning_style=learning_style,
            agent_roles=agent_roles,
            coordination_pattern=coordination_pattern
        )
    
    def _create_constellation_agents(self, config: ConstellationConfig) -> List[Agent]:
        """Create agents for the constellation based on configuration"""
        agents = []
        
        for role in config.agent_roles:
            agent = self._create_specialized_agent(role, config)
            if agent:
                agents.append(agent)
        
        return agents
    
    def _create_specialized_agent(self, role: str, config: ConstellationConfig) -> Optional[Agent]:
        """Create a specialized agent for a specific role"""
        
        # Common LLM for all agents
        llm = ChatTogether(model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free")
        
        # Role-specific agent creation
        if role == "research_specialist":
            return Agent(
                llm=llm,
                description=f"Framework research specialist for {config.framework}",
                skills=[
                    f"Research latest {config.framework} information and updates",
                    "Find relevant documentation and resources",
                    "Identify best practices and common patterns",
                    "Compare different approaches and methodologies"
                ],
                tools=['tools.educational_tools.framework_research'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        elif role == "concept_explainer":
            return Agent(
                llm=llm,
                description=f"Concept explainer for {config.framework} at {config.experience_level} level",
                skills=[
                    f"Explain {config.framework} concepts clearly",
                    f"Adapt explanations to {config.experience_level} level",
                    "Provide examples and analogies",
                    "Break down complex topics into digestible parts"
                ],
                tools=['tools.educational_tools.concept_explainer'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        elif role == "tutorial_guide":
            return Agent(
                llm=llm,
                description=f"Step-by-step tutorial guide for {config.framework}",
                skills=[
                    f"Create step-by-step {config.framework} tutorials",
                    "Guide learners through practical implementations",
                    "Provide clear instructions and checkpoints",
                    "Adapt pace to learner progress"
                ],
                tools=['tools.educational_tools.tutorial_generator'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        elif role == "exercise_generator":
            return Agent(
                llm=llm,
                description=f"Exercise generator for {config.framework} practice",
                skills=[
                    f"Generate hands-on {config.framework} exercises",
                    f"Create {config.experience_level}-appropriate challenges",
                    "Design progressive difficulty exercises",
                    "Provide exercise solutions and explanations"
                ],
                tools=['tools.educational_tools.exercise_generator'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        elif role == "quiz_generator":
            return Agent(
                llm=llm,
                description=f"Interactive quiz generator for {config.framework}",
                skills=[
                    f"Create {config.framework} knowledge assessments",
                    "Generate multiple choice and practical questions",
                    "Adapt difficulty to learner level",
                    "Provide detailed feedback on answers"
                ],
                tools=['tools.educational_tools.quiz_generator'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        elif role == "progress_tracker":
            return Agent(
                llm=llm,
                description=f"Learning progress tracker for {config.framework}",
                skills=[
                    "Track learning progress and milestones",
                    "Identify knowledge gaps and strengths",
                    "Recommend next learning steps",
                    "Generate progress reports"
                ],
                tools=['tools.educational_tools.progress_tracker'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
        
        else:
            # Generic learning agent for unspecified roles
            return Agent(
                llm=llm,
                description=f"Learning assistant for {config.framework} - {role}",
                skills=[
                    f"Assist with {config.framework} learning",
                    f"Support {config.experience_level} level learners",
                    "Provide educational guidance",
                    "Adapt to learner needs"
                ],
                tools=['tools.educational_tools.general_learning'],
                memory_path=f'data/sessions/{role}_memory.json'
            )
    
    def _load_constellation_templates(self) -> Dict[str, Any]:
        """Load constellation templates for different learning scenarios"""
        return {
            "langchain_beginner": {
                "agents": ["research_specialist", "concept_explainer", "tutorial_guide"],
                "coordination": "sequential_with_feedback"
            },
            "crewai_intermediate": {
                "agents": ["tutorial_guide", "exercise_generator", "progress_tracker"],
                "coordination": "interactive_guidance"
            },
            "multi_agent_advanced": {
                "agents": ["research_specialist", "exercise_generator", "quiz_generator"],
                "coordination": "adaptive_coordination"
            }
        }
    
    def get_constellation(self, constellation_id: str) -> Optional[LearningConstellation]:
        """Get active constellation by ID"""
        return self.active_constellations.get(constellation_id)
    
    def deactivate_constellation(self, constellation_id: str) -> bool:
        """Deactivate and remove constellation"""
        if constellation_id in self.active_constellations:
            constellation = self.active_constellations[constellation_id]
            constellation.deactivate()
            del self.active_constellations[constellation_id]
            logger.info(f"Constellation {constellation_id} deactivated and removed")
            return True
        return False
    
    def list_active_constellations(self) -> List[str]:
        """List all active constellation IDs"""
        return list(self.active_constellations.keys())
