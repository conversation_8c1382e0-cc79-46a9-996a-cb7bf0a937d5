"""
Learning Orchestrator - Main coordinator for educational learning sessions
Extends VinAgent's Agent class for educational purposes
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import logging

from vinagent.agent.agent import Agent
from langchain_together import ChatTogether
from vinagent.memory.memory import Memory

from .constellation_manager import Constellation<PERSON>anager
from .context_analyzer import ContextAnalyzer
from .progress_synthesizer import ProgressSynthesizer
from .adaptive_engine import AdaptiveEngine

logger = logging.getLogger(__name__)

@dataclass
class LearningRequest:
    """Learning request structure"""
    learner_profile: Dict[str, Any]
    learning_objective: str
    target_framework: str
    session_id: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

@dataclass
class LearningContext:
    """Learning context analysis result"""
    target_framework: str
    experience_level: str
    learning_phase: str
    learning_style: str
    complexity_needed: str
    current_phase: str

@dataclass
class LearningSession:
    """Learning session state"""
    session_id: str
    user_profile: Dict[str, Any]
    constellation: Any
    objective: str
    context: LearningContext
    start_time: datetime
    current_state: Dict[str, Any]

class LearningOrchestrator(Agent):
    """
    Main learning coordinator that extends VinAgent's Agent class
    Manages the overall learning workflow and constellation formation
    """
    
    def __init__(self, llm: Optional[ChatTogether] = None):
        # Initialize with default LLM if none provided
        if llm is None:
            llm = ChatTogether(model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free")
        
        super().__init__(
            llm=llm,
            description="Educational Learning Orchestrator for AI Framework Learning",
            skills=[
                "Analyze learning context and user experience levels",
                "Form optimal learning constellations based on user needs",
                "Coordinate educational agent workflows",
                "Track and adapt learning progress dynamically",
                "Provide personalized learning experiences"
            ],
            tools=[
                'tools.educational_tools.framework_research',
                'tools.educational_tools.concept_explainer',
                'tools.educational_tools.progress_tracker'
            ],
            memory_path='data/sessions/learning_memory.json',
            is_reset_memory=False
        )
        
        # Initialize core components
        self.constellation_manager = ConstellationManager()
        self.context_analyzer = ContextAnalyzer()
        self.progress_synthesizer = ProgressSynthesizer()
        self.adaptive_engine = AdaptiveEngine()
        
        # Session management
        self.active_sessions: Dict[str, LearningSession] = {}
        
        logger.info("LearningOrchestrator initialized with VinAgent core")
    
    def start_learning_session(self, request: LearningRequest) -> LearningSession:
        """
        Initialize a new learning session with constellation formation
        
        Args:
            request: Learning request with user profile and objectives
            
        Returns:
            LearningSession: Initialized learning session
        """
        logger.info(f"Starting learning session for framework: {request.target_framework}")
        
        # Phase 1: Context Analysis
        context = self.analyze_learning_context(request.learner_profile, request.learning_objective)
        
        # Phase 2: Constellation Formation
        constellation = self.constellation_manager.form_constellation(
            framework=context.target_framework,
            experience_level=context.experience_level,
            learning_phase=context.learning_phase,
            learning_style=context.learning_style
        )
        
        # Phase 3: Session Initialization
        session = self._create_session(request, constellation, context)
        
        # Store active session
        self.active_sessions[session.session_id] = session
        
        logger.info(f"Learning session {session.session_id} created successfully")
        return session
    
    def analyze_learning_context(self, user_profile: Dict[str, Any], objective: str) -> LearningContext:
        """
        Analyze learning context to determine optimal approach
        
        Args:
            user_profile: User's learning profile and experience
            objective: Learning objective description
            
        Returns:
            LearningContext: Analyzed learning context
        """
        # Determine target framework from objective
        target_framework = self._extract_framework_from_objective(objective)
        
        # Assess user experience level
        experience_level = self._assess_experience_level(user_profile, target_framework)
        
        # Determine appropriate learning phase
        learning_phase = self._determine_learning_phase(experience_level, objective)
        
        # Extract learning style preferences
        learning_style = user_profile.get('learning_preferences', {}).get('style', 'hands_on_with_theory')
        
        # Determine complexity level needed
        complexity_needed = self._determine_complexity_level(experience_level, objective)
        
        # Determine current phase
        current_phase = self._determine_current_phase(learning_phase, experience_level)
        
        return LearningContext(
            target_framework=target_framework,
            experience_level=experience_level,
            learning_phase=learning_phase,
            learning_style=learning_style,
            complexity_needed=complexity_needed,
            current_phase=current_phase
        )
    
    def _extract_framework_from_objective(self, objective: str) -> str:
        """Extract target framework from learning objective"""
        objective_lower = objective.lower()
        
        if 'langchain' in objective_lower:
            return 'langchain'
        elif 'crewai' in objective_lower or 'crew ai' in objective_lower:
            return 'crewai'
        elif 'multi-agent' in objective_lower or 'multi agent' in objective_lower:
            return 'multi_agent_orchestration'
        else:
            # Default to langchain for basic AI framework learning
            return 'langchain'
    
    def _assess_experience_level(self, user_profile: Dict[str, Any], framework: str) -> str:
        """Assess user's experience level with the target framework"""
        framework_experience = user_profile.get('framework_experience', {})
        
        if framework in framework_experience:
            exp_level = framework_experience[framework]
            if exp_level in ['none', 'beginner']:
                return 'beginner'
            elif exp_level == 'intermediate':
                return 'intermediate'
            else:
                return 'advanced'
        
        # Fallback to general AI framework experience
        ai_experience = user_profile.get('experience_levels', {}).get('ai_frameworks', 'beginner')
        return ai_experience if ai_experience in ['beginner', 'intermediate', 'advanced'] else 'beginner'
    
    def _determine_learning_phase(self, experience_level: str, objective: str) -> str:
        """Determine appropriate learning phase based on experience and objective"""
        if experience_level == 'beginner':
            return 'introduction'
        elif 'basic' in objective.lower() or 'introduction' in objective.lower():
            return 'basic_concepts'
        elif 'practice' in objective.lower() or 'hands-on' in objective.lower():
            return 'hands_on_practice'
        elif 'advanced' in objective.lower():
            return 'advanced_concepts'
        else:
            return 'basic_concepts'
    
    def _determine_complexity_level(self, experience_level: str, objective: str) -> str:
        """Determine complexity level needed for learning"""
        if experience_level == 'beginner':
            return 'basic'
        elif experience_level == 'intermediate':
            return 'intermediate'
        else:
            return 'advanced'
    
    def _determine_current_phase(self, learning_phase: str, experience_level: str) -> str:
        """Determine current learning phase for constellation formation"""
        if learning_phase == 'introduction':
            return 'knowledge_acquisition'
        elif learning_phase in ['basic_concepts', 'hands_on_practice']:
            return 'practice_focused'
        else:
            return 'advanced_application'
    
    def _create_session(self, request: LearningRequest, constellation: Any, context: LearningContext) -> LearningSession:
        """Create a new learning session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return LearningSession(
            session_id=session_id,
            user_profile=request.learner_profile,
            constellation=constellation,
            objective=request.learning_objective,
            context=context,
            start_time=datetime.now(),
            current_state={
                'phase': context.current_phase,
                'progress': 0.0,
                'completed_modules': [],
                'current_module': None
            }
        )
    
    def get_session(self, session_id: str) -> Optional[LearningSession]:
        """Get active learning session by ID"""
        return self.active_sessions.get(session_id)
    
    def update_session_progress(self, session_id: str, progress_data: Dict[str, Any]) -> bool:
        """Update learning session progress"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.current_state.update(progress_data)
            
            # Use progress synthesizer to analyze progress
            self.progress_synthesizer.update_progress(session_id, progress_data)
            
            return True
        return False
    
    def end_session(self, session_id: str) -> bool:
        """End a learning session"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            
            # Save session data to memory
            session_summary = {
                'session_id': session_id,
                'framework': session.context.target_framework,
                'duration': (datetime.now() - session.start_time).total_seconds(),
                'final_progress': session.current_state.get('progress', 0.0),
                'completed_modules': session.current_state.get('completed_modules', [])
            }
            
            # Store in VinAgent memory
            if self.memory:
                self.memory.save_short_term_memory(
                    self.llm,
                    f"Completed learning session: {session_summary}",
                    user_id=session.user_profile.get('learner_id', 'unknown')
                )
            
            # Remove from active sessions
            del self.active_sessions[session_id]
            
            logger.info(f"Learning session {session_id} ended successfully")
            return True
        
        return False
