"""
Educational Learning Topology (ELT) System based on VinAgent
"""

__version__ = "0.1.0"
__author__ = "Educational Agent System"

from .core.learning_orchestrator import LearningOrchestrator
from .core.constellation_manager import ConstellationManager
from .core.context_analyzer import ContextAnalyzer
from .core.progress_synthesizer import ProgressSynthesizer

__all__ = [
    "LearningOrchestrator",
    "ConstellationManager", 
    "ContextAnalyzer",
    "ProgressSynthesizer"
]
